import 'dart:io';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class CameraService {
  static CameraService? _instance;
  static List<CameraDescription>? _cameras;
  
  CameraController? _controller;
  final ImagePicker _imagePicker = ImagePicker();

  CameraService._();

  static Future<CameraService> getInstance() async {
    if (_instance == null) {
      _instance = CameraService._();
      await _instance!._initializeCameras();
    }
    return _instance!;
  }

  Future<void> _initializeCameras() async {
    try {
      _cameras = await availableCameras();
    } catch (e) {
      print('Error initializing cameras: $e');
      _cameras = [];
    }
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Check if storage permission is granted (for Android)
  Future<bool> hasStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.status;
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission for app documents
  }

  /// Request storage permission (for Android)
  Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true;
  }

  /// Initialize camera controller
  Future<CameraController?> initializeCamera({
    CameraLensDirection preferredDirection = CameraLensDirection.back,
  }) async {
    if (_cameras == null || _cameras!.isEmpty) {
      throw CameraException('No cameras available', 'No cameras found on device');
    }

    // Find the preferred camera
    CameraDescription? selectedCamera;
    for (final camera in _cameras!) {
      if (camera.lensDirection == preferredDirection) {
        selectedCamera = camera;
        break;
      }
    }

    // If preferred camera not found, use the first available
    selectedCamera ??= _cameras!.first;

    _controller = CameraController(
      selectedCamera,
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    try {
      await _controller!.initialize();
      return _controller;
    } catch (e) {
      throw CameraException('Failed to initialize camera', e.toString());
    }
  }

  /// Take a picture using the camera controller
  Future<File> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized', 'Camera controller is not initialized');
    }

    try {
      final XFile picture = await _controller!.takePicture();
      return File(picture.path);
    } catch (e) {
      throw CameraException('Failed to take picture', e.toString());
    }
  }

  /// Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw CameraException('Failed to pick image from gallery', e.toString());
    }
  }

  /// Take picture using image picker (system camera)
  Future<File?> takePictureWithImagePicker() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw CameraException('Failed to take picture with image picker', e.toString());
    }
  }

  /// Save image to app documents directory
  Future<File> saveImageToAppDirectory(File imageFile) async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String imagesDir = path.join(appDocDir.path, 'images');
      
      // Create images directory if it doesn't exist
      final Directory imagesDirObj = Directory(imagesDir);
      if (!await imagesDirObj.exists()) {
        await imagesDirObj.create(recursive: true);
      }

      // Generate unique filename
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String extension = path.extension(imageFile.path);
      final String fileName = 'meal_$timestamp$extension';
      final String newPath = path.join(imagesDir, fileName);

      // Copy file to new location
      final File newFile = await imageFile.copy(newPath);
      
      // Delete original file if it's in a temporary location
      if (imageFile.path.contains('cache') || imageFile.path.contains('tmp')) {
        try {
          await imageFile.delete();
        } catch (e) {
          // Ignore deletion errors for temporary files
        }
      }

      return newFile;
    } catch (e) {
      throw CameraException('Failed to save image', e.toString());
    }
  }

  /// Get available cameras
  List<CameraDescription> getAvailableCameras() {
    return _cameras ?? [];
  }

  /// Check if device has camera
  bool hasCamera() {
    return _cameras != null && _cameras!.isNotEmpty;
  }

  /// Check if device has front camera
  bool hasFrontCamera() {
    if (_cameras == null) return false;
    return _cameras!.any((camera) => camera.lensDirection == CameraLensDirection.front);
  }

  /// Check if device has back camera
  bool hasBackCamera() {
    if (_cameras == null) return false;
    return _cameras!.any((camera) => camera.lensDirection == CameraLensDirection.back);
  }

  /// Switch camera (front/back)
  Future<CameraController?> switchCamera() async {
    if (_controller == null) return null;

    final currentDirection = _controller!.description.lensDirection;
    final newDirection = currentDirection == CameraLensDirection.back
        ? CameraLensDirection.front
        : CameraLensDirection.back;

    await disposeController();
    return await initializeCamera(preferredDirection: newDirection);
  }

  /// Set flash mode
  Future<void> setFlashMode(FlashMode flashMode) async {
    if (_controller != null && _controller!.value.isInitialized) {
      try {
        await _controller!.setFlashMode(flashMode);
      } catch (e) {
        // Flash mode might not be supported on all devices
        print('Failed to set flash mode: $e');
      }
    }
  }

  /// Get current flash mode
  FlashMode? getCurrentFlashMode() {
    return _controller?.value.flashMode;
  }

  /// Check if flash is available
  bool isFlashAvailable() {
    return _controller?.value.isInitialized == true;
  }

  /// Dispose camera controller
  Future<void> disposeController() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
  }

  /// Get current camera controller
  CameraController? get controller => _controller;

  /// Check if camera is initialized
  bool get isInitialized => _controller?.value.isInitialized ?? false;

  /// Clean up old images (keep only last 50 images)
  Future<void> cleanupOldImages() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String imagesDir = path.join(appDocDir.path, 'images');
      final Directory imagesDirObj = Directory(imagesDir);

      if (await imagesDirObj.exists()) {
        final List<FileSystemEntity> files = await imagesDirObj.list().toList();
        final List<File> imageFiles = files
            .whereType<File>()
            .where((file) => file.path.toLowerCase().endsWith('.jpg') || 
                           file.path.toLowerCase().endsWith('.jpeg') ||
                           file.path.toLowerCase().endsWith('.png'))
            .toList();

        // Sort by modification date (newest first)
        imageFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

        // Delete files beyond the limit (keep 50 most recent)
        const int maxFiles = 50;
        if (imageFiles.length > maxFiles) {
          for (int i = maxFiles; i < imageFiles.length; i++) {
            try {
              await imageFiles[i].delete();
            } catch (e) {
              print('Failed to delete old image: ${imageFiles[i].path}');
            }
          }
        }
      }
    } catch (e) {
      print('Failed to cleanup old images: $e');
    }
  }

  /// Get image file size in bytes
  Future<int> getImageFileSize(File imageFile) async {
    try {
      return await imageFile.length();
    } catch (e) {
      return 0;
    }
  }

  /// Check if image file exists
  Future<bool> imageFileExists(String imagePath) async {
    try {
      return await File(imagePath).exists();
    } catch (e) {
      return false;
    }
  }

  /// Delete image file
  Future<bool> deleteImageFile(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Failed to delete image file: $e');
      return false;
    }
  }
}
