import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/app_state_provider.dart';
import '../models/models.dart';
import '../widgets/widgets.dart';
import 'camera_screen.dart';
import 'settings_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Meal Vision Tracker'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: Consumer<AppStateProvider>(
        builder: (context, appState, child) {
          if (!appState.isInitialized) {
            return const Center(child: CircularProgressIndicator());
          }

          if (appState.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${appState.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => appState.refreshTodaysData(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (!appState.hasApiKey) {
            return const _ApiKeySetupWidget();
          }

          return RefreshIndicator(
            onRefresh: () => appState.refreshTodaysData(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCaloriesSummaryCard(context, appState),
                  const SizedBox(height: 16),
                  _buildMacronutrientsCard(context, appState),
                  const SizedBox(height: 16),
                  _buildTodaysMealsCard(context, appState),
                  const SizedBox(height: 16),
                  _buildQuickActionsCard(context),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: Consumer<AppStateProvider>(
        builder: (context, appState, child) {
          if (!appState.hasApiKey) return const SizedBox.shrink();
          
          return FloatingActionButton.extended(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CameraScreen()),
              );
            },
            icon: const Icon(Icons.camera_alt),
            label: const Text('Add Meal'),
          );
        },
      ),
    );
  }

  Widget _buildCaloriesSummaryCard(BuildContext context, AppStateProvider appState) {
    final summary = appState.todaysNutritionSummary;
    final calories = summary['calories'] as double;
    final calorieProgress = summary['calorieProgress'] as double;
    final goal = appState.currentDailyLog?.goals.calorieGoal ?? 2000;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Today\'s Calories',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Icon(
                  Icons.local_fire_department,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${calories.toStringAsFixed(0)} / ${goal.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: calorieProgress.clamp(0.0, 1.0),
                        backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${(calorieProgress * 100).toStringAsFixed(0)}% of goal',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    value: calorieProgress.clamp(0.0, 1.0),
                    strokeWidth: 8,
                    backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMacronutrientsCard(BuildContext context, AppStateProvider appState) {
    final summary = appState.todaysNutritionSummary;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Macronutrients',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMacroColumn(
                    context,
                    'Protein',
                    summary['protein'] as double,
                    summary['proteinProgress'] as double,
                    Colors.red,
                    'g',
                  ),
                ),
                Expanded(
                  child: _buildMacroColumn(
                    context,
                    'Carbs',
                    summary['carbohydrates'] as double,
                    summary['carbohydrateProgress'] as double,
                    Colors.orange,
                    'g',
                  ),
                ),
                Expanded(
                  child: _buildMacroColumn(
                    context,
                    'Fat',
                    summary['fat'] as double,
                    summary['fatProgress'] as double,
                    Colors.blue,
                    'g',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMacroColumn(
    BuildContext context,
    String label,
    double value,
    double progress,
    Color color,
    String unit,
  ) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${value.toStringAsFixed(0)}$unit',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            strokeWidth: 6,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            backgroundColor: color.withOpacity(0.2),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${(progress * 100).toStringAsFixed(0)}%',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildTodaysMealsCard(BuildContext context, AppStateProvider appState) {
    final mealsByType = appState.todaysMealsByType;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Meals',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (mealsByType.isEmpty || mealsByType.values.every((meals) => meals.isEmpty))
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.restaurant_menu,
                      size: 48,
                      color: Theme.of(context).colorScheme.outline,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No meals logged today',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap the camera button to add your first meal!',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              ...MealType.values.map((mealType) {
                final meals = mealsByType[mealType] ?? [];
                if (meals.isEmpty) return const SizedBox.shrink();
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getMealTypeDisplayName(mealType),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...meals.map((meal) => MealListItem(
                      meal: meal,
                      onTap: () => _showMealDetails(context, meal),
                    )),
                    const SizedBox(height: 16),
                  ],
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    context,
                    'Camera',
                    Icons.camera_alt,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const CameraScreen()),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    context,
                    'Gallery',
                    Icons.photo_library,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CameraScreen(useGallery: true),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 8),
          Text(label),
        ],
      ),
    );
  }

  String _getMealTypeDisplayName(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return 'Breakfast';
      case MealType.lunch:
        return 'Lunch';
      case MealType.dinner:
        return 'Dinner';
      case MealType.snack:
        return 'Snacks';
      case MealType.other:
        return 'Other';
    }
  }

  void _showMealDetails(BuildContext context, Meal meal) {
    // TODO: Implement meal details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Meal details for ${meal.name}')),
    );
  }
}

class _ApiKeySetupWidget extends StatefulWidget {
  const _ApiKeySetupWidget();

  @override
  State<_ApiKeySetupWidget> createState() => _ApiKeySetupWidgetState();
}

class _ApiKeySetupWidgetState extends State<_ApiKeySetupWidget> {
  final _controller = TextEditingController();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.key,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 24),
          Text(
            'Setup Required',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          Text(
            'Please enter your Gemini API key to start using the app.',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          TextField(
            controller: _controller,
            decoration: const InputDecoration(
              labelText: 'Gemini API Key',
              hintText: 'Enter your API key here',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveApiKey,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Save API Key'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveApiKey() async {
    final apiKey = _controller.text.trim();
    if (apiKey.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter an API key')),
      );
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      await context.read<AppStateProvider>().setApiKey(apiKey);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('API key saved successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
