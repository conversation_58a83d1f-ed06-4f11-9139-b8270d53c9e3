import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import '../models/models.dart';

class GeminiApiService {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  static const String _model = 'gemini-1.5-flash';
  
  final String _apiKey;
  final http.Client _httpClient;

  GeminiApiService({
    required String apiKey,
    http.Client? httpClient,
  }) : _apiKey = apiKey,
       _httpClient = httpClient ?? http.Client();

  /// Analyze a food image and extract nutritional information
  Future<List<Food>> analyzeFoodImage(File imageFile) async {
    try {
      // Preprocess the image
      final processedImageBytes = await _preprocessImage(imageFile);
      
      // Convert image to base64
      final base64Image = base64Encode(processedImageBytes);
      
      // Create the request payload
      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': _getFoodAnalysisPrompt(),
              },
              {
                'inline_data': {
                  'mime_type': 'image/jpeg',
                  'data': base64Image,
                }
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.1,
          'topK': 32,
          'topP': 1,
          'maxOutputTokens': 2048,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      // Make the API request
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/models/$_model:generateContent?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return _parseGeminiResponse(responseData);
      } else {
        throw GeminiApiException(
          'API request failed with status ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      if (e is GeminiApiException) rethrow;
      throw GeminiApiException('Failed to analyze image: $e');
    }
  }

  /// Preprocess image to optimize for API consumption
  Future<Uint8List> _preprocessImage(File imageFile) async {
    try {
      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw const GeminiApiException('Failed to decode image');
      }

      // Resize image if too large (max 4MB for Gemini API)
      img.Image processedImage = image;
      
      // Resize if width or height is too large
      if (image.width > 2048 || image.height > 2048) {
        processedImage = img.copyResize(
          image,
          width: image.width > image.height ? 2048 : null,
          height: image.height > image.width ? 2048 : null,
        );
      }

      // Convert to JPEG with quality compression
      final compressedBytes = img.encodeJpg(processedImage, quality: 85);
      
      // Check file size (4MB limit)
      if (compressedBytes.length > 4 * 1024 * 1024) {
        // Further compress if still too large
        final furtherCompressed = img.encodeJpg(processedImage, quality: 70);
        return Uint8List.fromList(furtherCompressed);
      }
      
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      throw GeminiApiException('Failed to preprocess image: $e');
    }
  }

  /// Generate the prompt for food analysis
  String _getFoodAnalysisPrompt() {
    return '''
Analyze this food image and provide detailed nutritional information. Please respond with a JSON array containing information for each food item visible in the image.

For each food item, provide the following information:
- name: The name of the food item
- description: A brief description of the food
- category: Food category (e.g., "Fruits", "Vegetables", "Grains", "Protein", "Dairy", "Snacks", "Beverages")
- estimatedWeight: Estimated weight in grams for the portion shown
- confidence: Your confidence level in the identification (0.0 to 1.0)
- nutrition: Nutritional information per 100g containing:
  - calories: Calories per 100g
  - protein: Protein in grams per 100g
  - carbohydrates: Carbohydrates in grams per 100g
  - fat: Fat in grams per 100g
  - fiber: Fiber in grams per 100g (optional, default 0)
  - sugar: Sugar in grams per 100g (optional, default 0)
  - sodium: Sodium in milligrams per 100g (optional, default 0)
  - cholesterol: Cholesterol in milligrams per 100g (optional, default 0)
  - vitaminC: Vitamin C in milligrams per 100g (optional, default 0)
  - calcium: Calcium in milligrams per 100g (optional, default 0)
  - iron: Iron in milligrams per 100g (optional, default 0)

Example response format:
[
  {
    "name": "Grilled Chicken Breast",
    "description": "Lean grilled chicken breast, appears to be seasoned",
    "category": "Protein",
    "estimatedWeight": 150,
    "confidence": 0.9,
    "nutrition": {
      "calories": 165,
      "protein": 31,
      "carbohydrates": 0,
      "fat": 3.6,
      "fiber": 0,
      "sugar": 0,
      "sodium": 74,
      "cholesterol": 85,
      "vitaminC": 0,
      "calcium": 15,
      "iron": 1
    }
  }
]

Please analyze the image carefully and provide accurate nutritional estimates. If you cannot clearly identify a food item, set the confidence level accordingly. Only respond with the JSON array, no additional text.
''';
  }

  /// Parse the Gemini API response and convert to Food objects
  List<Food> _parseGeminiResponse(Map<String, dynamic> responseData) {
    try {
      final candidates = responseData['candidates'] as List?;
      if (candidates == null || candidates.isEmpty) {
        throw const GeminiApiException('No candidates in response');
      }

      final content = candidates[0]['content'];
      final parts = content['parts'] as List;
      final text = parts[0]['text'] as String;

      // Clean the response text (remove markdown formatting if present)
      String cleanedText = text.trim();
      if (cleanedText.startsWith('```json')) {
        cleanedText = cleanedText.substring(7);
      }
      if (cleanedText.endsWith('```')) {
        cleanedText = cleanedText.substring(0, cleanedText.length - 3);
      }
      cleanedText = cleanedText.trim();

      // Parse JSON response
      final List<dynamic> foodData = jsonDecode(cleanedText);
      
      return foodData.map((item) => _createFoodFromGeminiData(item)).toList();
    } catch (e) {
      throw GeminiApiException('Failed to parse Gemini response: $e');
    }
  }

  /// Create a Food object from Gemini API data
  Food _createFoodFromGeminiData(Map<String, dynamic> data) {
    try {
      final nutritionData = data['nutrition'] as Map<String, dynamic>;
      
      final nutrition = NutritionInfo(
        calories: (nutritionData['calories'] as num).toDouble(),
        protein: (nutritionData['protein'] as num).toDouble(),
        carbohydrates: (nutritionData['carbohydrates'] as num).toDouble(),
        fat: (nutritionData['fat'] as num).toDouble(),
        fiber: (nutritionData['fiber'] as num?)?.toDouble() ?? 0.0,
        sugar: (nutritionData['sugar'] as num?)?.toDouble() ?? 0.0,
        sodium: (nutritionData['sodium'] as num?)?.toDouble() ?? 0.0,
        cholesterol: (nutritionData['cholesterol'] as num?)?.toDouble() ?? 0.0,
        vitaminC: (nutritionData['vitaminC'] as num?)?.toDouble() ?? 0.0,
        calcium: (nutritionData['calcium'] as num?)?.toDouble() ?? 0.0,
        iron: (nutritionData['iron'] as num?)?.toDouble() ?? 0.0,
      );

      return Food.fromGeminiResponse(
        name: data['name'] as String,
        description: data['description'] as String,
        category: data['category'] as String,
        nutrition: nutrition,
        confidence: (data['confidence'] as num).toDouble(),
        tags: (data['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      );
    } catch (e) {
      throw GeminiApiException('Failed to create Food object: $e');
    }
  }

  /// Get estimated serving size from Gemini response
  double _getEstimatedServingSize(Map<String, dynamic> data) {
    return (data['estimatedWeight'] as num?)?.toDouble() ?? 100.0;
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
  }
}

/// Custom exception for Gemini API errors
class GeminiApiException implements Exception {
  final String message;
  
  const GeminiApiException(this.message);
  
  @override
  String toString() => 'GeminiApiException: $message';
}
