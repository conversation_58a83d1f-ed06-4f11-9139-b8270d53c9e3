// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'food.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Food _$FoodFromJson(Map<String, dynamic> json) => Food(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  nutritionPer100g: NutritionInfo.fromJson(
    json['nutritionPer100g'] as Map<String, dynamic>,
  ),
  category: json['category'] as String,
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  confidence: (json['confidence'] as num?)?.toDouble() ?? 1.0,
  imageUrl: json['imageUrl'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$FoodToJson(Food instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'nutritionPer100g': instance.nutritionPer100g,
  'category': instance.category,
  'tags': instance.tags,
  'confidence': instance.confidence,
  'imageUrl': instance.imageUrl,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
