import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/models.dart';

class DatabaseService {
  static const String _databaseName = 'meal_vision_tracker.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _foodsTable = 'foods';
  static const String _mealsTable = 'meals';
  static const String _foodItemsTable = 'food_items';
  static const String _dailyLogsTable = 'daily_logs';

  static Database? _database;
  static DatabaseService? _instance;

  DatabaseService._();

  static Future<DatabaseService> getInstance() async {
    if (_instance == null) {
      _instance = DatabaseService._();
      await _instance!._initDatabase();
    }
    return _instance!;
  }

  Future<void> _initDatabase() async {
    if (_database != null) return;

    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    _database = await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Create foods table
    await db.execute('''
      CREATE TABLE $_foodsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT NOT NULL,
        confidence REAL NOT NULL,
        image_url TEXT,
        nutrition_data TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create meals table
    await db.execute('''
      CREATE TABLE $_mealsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        image_url TEXT,
        notes TEXT,
        is_manually_adjusted INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create food_items table (junction table for meals and foods)
    await db.execute('''
      CREATE TABLE $_foodItemsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        meal_id TEXT NOT NULL,
        food_id TEXT NOT NULL,
        serving_size_grams REAL NOT NULL,
        notes TEXT,
        FOREIGN KEY (meal_id) REFERENCES $_mealsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (food_id) REFERENCES $_foodsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create daily_logs table
    await db.execute('''
      CREATE TABLE $_dailyLogsTable (
        id TEXT PRIMARY KEY,
        date INTEGER NOT NULL UNIQUE,
        goals_data TEXT NOT NULL,
        weight REAL,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_meals_timestamp ON $_mealsTable (timestamp)');
    await db.execute('CREATE INDEX idx_daily_logs_date ON $_dailyLogsTable (date)');
    await db.execute('CREATE INDEX idx_food_items_meal_id ON $_foodItemsTable (meal_id)');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    // For now, we'll just recreate the tables
    if (oldVersion < newVersion) {
      await db.execute('DROP TABLE IF EXISTS $_foodItemsTable');
      await db.execute('DROP TABLE IF EXISTS $_mealsTable');
      await db.execute('DROP TABLE IF EXISTS $_foodsTable');
      await db.execute('DROP TABLE IF EXISTS $_dailyLogsTable');
      await _createTables(db, newVersion);
    }
  }

  // Food operations
  Future<void> insertFood(Food food) async {
    final db = _database!;
    await db.insert(
      _foodsTable,
      {
        'id': food.id,
        'name': food.name,
        'description': food.description,
        'category': food.category,
        'tags': jsonEncode(food.tags),
        'confidence': food.confidence,
        'image_url': food.imageUrl,
        'nutrition_data': jsonEncode(food.nutritionPer100g.toJson()),
        'created_at': food.createdAt.millisecondsSinceEpoch,
        'updated_at': food.updatedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<Food?> getFoodById(String id) async {
    final db = _database!;
    final maps = await db.query(
      _foodsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return _foodFromMap(maps.first);
  }

  Future<List<Food>> getAllFoods() async {
    final db = _database!;
    final maps = await db.query(_foodsTable, orderBy: 'created_at DESC');
    return maps.map(_foodFromMap).toList();
  }

  Future<List<Food>> searchFoods(String query) async {
    final db = _database!;
    final maps = await db.query(
      _foodsTable,
      where: 'name LIKE ? OR description LIKE ? OR category LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'confidence DESC, created_at DESC',
    );
    return maps.map(_foodFromMap).toList();
  }

  // Meal operations
  Future<void> insertMeal(Meal meal) async {
    final db = _database!;
    
    await db.transaction((txn) async {
      // Insert meal
      await txn.insert(
        _mealsTable,
        {
          'id': meal.id,
          'name': meal.name,
          'type': meal.type.name,
          'timestamp': meal.timestamp.millisecondsSinceEpoch,
          'image_url': meal.imageUrl,
          'notes': meal.notes,
          'is_manually_adjusted': meal.isManuallyAdjusted ? 1 : 0,
          'created_at': meal.createdAt.millisecondsSinceEpoch,
          'updated_at': meal.updatedAt.millisecondsSinceEpoch,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Insert food items
      for (final foodItem in meal.foodItems) {
        // First ensure the food exists
        await insertFood(foodItem.food);
        
        // Then insert the food item
        await txn.insert(
          _foodItemsTable,
          {
            'meal_id': meal.id,
            'food_id': foodItem.food.id,
            'serving_size_grams': foodItem.servingSizeGrams,
            'notes': foodItem.notes,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  Future<Meal?> getMealById(String id) async {
    final db = _database!;
    final mealMaps = await db.query(
      _mealsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (mealMaps.isEmpty) return null;

    final mealMap = mealMaps.first;
    final foodItems = await _getFoodItemsForMeal(id);

    return _mealFromMap(mealMap, foodItems);
  }

  Future<List<Meal>> getMealsForDateRange(DateTime startDate, DateTime endDate) async {
    final db = _database!;
    final mealMaps = await db.query(
      _mealsTable,
      where: 'timestamp >= ? AND timestamp <= ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'timestamp DESC',
    );

    final meals = <Meal>[];
    for (final mealMap in mealMaps) {
      final foodItems = await _getFoodItemsForMeal(mealMap['id'] as String);
      meals.add(_mealFromMap(mealMap, foodItems));
    }

    return meals;
  }

  Future<List<Meal>> getMealsForDate(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1)).subtract(const Duration(milliseconds: 1));
    return getMealsForDateRange(startOfDay, endOfDay);
  }

  Future<void> deleteMeal(String id) async {
    final db = _database!;
    await db.transaction((txn) async {
      await txn.delete(_foodItemsTable, where: 'meal_id = ?', whereArgs: [id]);
      await txn.delete(_mealsTable, where: 'id = ?', whereArgs: [id]);
    });
  }

  // Daily log operations
  Future<void> insertOrUpdateDailyLog(DailyLog dailyLog) async {
    final db = _database!;
    await db.insert(
      _dailyLogsTable,
      {
        'id': dailyLog.id,
        'date': dailyLog.date.millisecondsSinceEpoch,
        'goals_data': jsonEncode(dailyLog.goals.toJson()),
        'weight': dailyLog.weight,
        'notes': dailyLog.notes,
        'created_at': dailyLog.createdAt.millisecondsSinceEpoch,
        'updated_at': dailyLog.updatedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<DailyLog?> getDailyLogForDate(DateTime date) async {
    final db = _database!;
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    final maps = await db.query(
      _dailyLogsTable,
      where: 'date = ?',
      whereArgs: [dateOnly.millisecondsSinceEpoch],
    );

    if (maps.isEmpty) return null;

    final logMap = maps.first;
    final meals = await getMealsForDate(date);

    return _dailyLogFromMap(logMap, meals);
  }

  Future<List<DailyLog>> getDailyLogsForDateRange(DateTime startDate, DateTime endDate) async {
    final db = _database!;
    final maps = await db.query(
      _dailyLogsTable,
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'date DESC',
    );

    final logs = <DailyLog>[];
    for (final map in maps) {
      final date = DateTime.fromMillisecondsSinceEpoch(map['date'] as int);
      final meals = await getMealsForDate(date);
      logs.add(_dailyLogFromMap(map, meals));
    }

    return logs;
  }

  // Helper methods
  Future<List<FoodItem>> _getFoodItemsForMeal(String mealId) async {
    final db = _database!;
    final maps = await db.rawQuery('''
      SELECT fi.serving_size_grams, fi.notes, f.*
      FROM $_foodItemsTable fi
      JOIN $_foodsTable f ON fi.food_id = f.id
      WHERE fi.meal_id = ?
    ''', [mealId]);

    return maps.map((map) {
      final food = _foodFromMap(map);
      return FoodItem(
        food: food,
        servingSizeGrams: map['serving_size_grams'] as double,
        notes: map['notes'] as String?,
      );
    }).toList();
  }

  Food _foodFromMap(Map<String, dynamic> map) {
    return Food(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String,
      category: map['category'] as String,
      tags: (jsonDecode(map['tags'] as String) as List).cast<String>(),
      confidence: map['confidence'] as double,
      imageUrl: map['image_url'] as String?,
      nutritionPer100g: NutritionInfo.fromJson(
        jsonDecode(map['nutrition_data'] as String),
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
    );
  }

  Meal _mealFromMap(Map<String, dynamic> map, List<FoodItem> foodItems) {
    return Meal(
      id: map['id'] as String,
      name: map['name'] as String,
      type: MealType.values.firstWhere((e) => e.name == map['type']),
      foodItems: foodItems,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] as int),
      imageUrl: map['image_url'] as String?,
      notes: map['notes'] as String?,
      isManuallyAdjusted: (map['is_manually_adjusted'] as int) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
    );
  }

  DailyLog _dailyLogFromMap(Map<String, dynamic> map, List<Meal> meals) {
    return DailyLog(
      id: map['id'] as String,
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] as int),
      meals: meals,
      goals: DailyGoals.fromJson(jsonDecode(map['goals_data'] as String)),
      weight: map['weight'] as double?,
      notes: map['notes'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
    );
  }

  // Database maintenance
  Future<void> clearAllData() async {
    final db = _database!;
    await db.transaction((txn) async {
      await txn.delete(_foodItemsTable);
      await txn.delete(_mealsTable);
      await txn.delete(_foodsTable);
      await txn.delete(_dailyLogsTable);
    });
  }

  Future<void> close() async {
    await _database?.close();
    _database = null;
    _instance = null;
  }
}
