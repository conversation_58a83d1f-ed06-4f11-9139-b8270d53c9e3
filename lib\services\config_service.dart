import 'package:shared_preferences/shared_preferences.dart';

class ConfigService {
  static const String _geminiApiKeyKey = 'gemini_api_key';
  static const String _defaultCalorieGoalKey = 'default_calorie_goal';
  static const String _defaultProteinGoalKey = 'default_protein_goal';
  static const String _defaultCarbGoalKey = 'default_carb_goal';
  static const String _defaultFatGoalKey = 'default_fat_goal';
  static const String _userWeightKey = 'user_weight';
  static const String _userHeightKey = 'user_height';
  static const String _userAgeKey = 'user_age';
  static const String _userGenderKey = 'user_gender';
  static const String _userActivityLevelKey = 'user_activity_level';

  static ConfigService? _instance;
  static SharedPreferences? _prefs;

  ConfigService._();

  static Future<ConfigService> getInstance() async {
    if (_instance == null) {
      _instance = ConfigService._();
      _prefs = await SharedPreferences.getInstance();
    }
    return _instance!;
  }

  // API Key Management
  Future<void> setGeminiApiKey(String apiKey) async {
    await _prefs!.setString(_geminiApiKeyKey, apiKey);
  }

  String? getGeminiApiKey() {
    return _prefs!.getString(_geminiApiKeyKey);
  }

  bool hasGeminiApiKey() {
    final apiKey = getGeminiApiKey();
    return apiKey != null && apiKey.isNotEmpty;
  }

  Future<void> clearGeminiApiKey() async {
    await _prefs!.remove(_geminiApiKeyKey);
  }

  // Daily Goals Management
  Future<void> setDefaultCalorieGoal(double goal) async {
    await _prefs!.setDouble(_defaultCalorieGoalKey, goal);
  }

  double getDefaultCalorieGoal() {
    return _prefs!.getDouble(_defaultCalorieGoalKey) ?? 2000.0;
  }

  Future<void> setDefaultProteinGoal(double goal) async {
    await _prefs!.setDouble(_defaultProteinGoalKey, goal);
  }

  double getDefaultProteinGoal() {
    return _prefs!.getDouble(_defaultProteinGoalKey) ?? 150.0;
  }

  Future<void> setDefaultCarbGoal(double goal) async {
    await _prefs!.setDouble(_defaultCarbGoalKey, goal);
  }

  double getDefaultCarbGoal() {
    return _prefs!.getDouble(_defaultCarbGoalKey) ?? 250.0;
  }

  Future<void> setDefaultFatGoal(double goal) async {
    await _prefs!.setDouble(_defaultFatGoalKey, goal);
  }

  double getDefaultFatGoal() {
    return _prefs!.getDouble(_defaultFatGoalKey) ?? 67.0;
  }

  // User Profile Management
  Future<void> setUserWeight(double weight) async {
    await _prefs!.setDouble(_userWeightKey, weight);
  }

  double? getUserWeight() {
    return _prefs!.getDouble(_userWeightKey);
  }

  Future<void> setUserHeight(double height) async {
    await _prefs!.setDouble(_userHeightKey, height);
  }

  double? getUserHeight() {
    return _prefs!.getDouble(_userHeightKey);
  }

  Future<void> setUserAge(int age) async {
    await _prefs!.setInt(_userAgeKey, age);
  }

  int? getUserAge() {
    return _prefs!.getInt(_userAgeKey);
  }

  Future<void> setUserGender(String gender) async {
    await _prefs!.setString(_userGenderKey, gender);
  }

  String? getUserGender() {
    return _prefs!.getString(_userGenderKey);
  }

  Future<void> setUserActivityLevel(String activityLevel) async {
    await _prefs!.setString(_userActivityLevelKey, activityLevel);
  }

  String? getUserActivityLevel() {
    return _prefs!.getString(_userActivityLevelKey);
  }

  // Calculate BMR (Basal Metabolic Rate) using Mifflin-St Jeor Equation
  double? calculateBMR() {
    final weight = getUserWeight();
    final height = getUserHeight();
    final age = getUserAge();
    final gender = getUserGender();

    if (weight == null || height == null || age == null || gender == null) {
      return null;
    }

    // Mifflin-St Jeor Equation
    // Men: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) + 5
    // Women: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) - 161

    double bmr = 10 * weight + 6.25 * height - 5 * age;
    if (gender.toLowerCase() == 'male') {
      bmr += 5;
    } else {
      bmr -= 161;
    }

    return bmr;
  }

  // Calculate TDEE (Total Daily Energy Expenditure)
  double? calculateTDEE() {
    final bmr = calculateBMR();
    final activityLevel = getUserActivityLevel();

    if (bmr == null || activityLevel == null) {
      return null;
    }

    // Activity multipliers
    final multipliers = {
      'sedentary': 1.2,        // Little or no exercise
      'lightly_active': 1.375, // Light exercise 1-3 days/week
      'moderately_active': 1.55, // Moderate exercise 3-5 days/week
      'very_active': 1.725,    // Hard exercise 6-7 days/week
      'extremely_active': 1.9, // Very hard exercise, physical job
    };

    final multiplier = multipliers[activityLevel] ?? 1.2;
    return bmr * multiplier;
  }

  // Get default daily goals based on user profile
  Map<String, double> getCalculatedDailyGoals() {
    final tdee = calculateTDEE();
    
    if (tdee == null) {
      // Return default goals if user profile is incomplete
      return {
        'calories': getDefaultCalorieGoal(),
        'protein': getDefaultProteinGoal(),
        'carbohydrates': getDefaultCarbGoal(),
        'fat': getDefaultFatGoal(),
      };
    }

    // Calculate macronutrient goals based on TDEE
    // Standard macro split: 30% protein, 40% carbs, 30% fat
    final proteinCalories = tdee * 0.30;
    final carbCalories = tdee * 0.40;
    final fatCalories = tdee * 0.30;

    return {
      'calories': tdee,
      'protein': proteinCalories / 4, // 4 calories per gram of protein
      'carbohydrates': carbCalories / 4, // 4 calories per gram of carbs
      'fat': fatCalories / 9, // 9 calories per gram of fat
    };
  }

  // Check if user profile is complete
  bool isUserProfileComplete() {
    return getUserWeight() != null &&
           getUserHeight() != null &&
           getUserAge() != null &&
           getUserGender() != null &&
           getUserActivityLevel() != null;
  }

  // Clear all user data
  Future<void> clearAllData() async {
    await _prefs!.clear();
  }

  // Export settings as JSON
  Map<String, dynamic> exportSettings() {
    return {
      'defaultCalorieGoal': getDefaultCalorieGoal(),
      'defaultProteinGoal': getDefaultProteinGoal(),
      'defaultCarbGoal': getDefaultCarbGoal(),
      'defaultFatGoal': getDefaultFatGoal(),
      'userWeight': getUserWeight(),
      'userHeight': getUserHeight(),
      'userAge': getUserAge(),
      'userGender': getUserGender(),
      'userActivityLevel': getUserActivityLevel(),
    };
  }

  // Import settings from JSON
  Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings['defaultCalorieGoal'] != null) {
      await setDefaultCalorieGoal(settings['defaultCalorieGoal'].toDouble());
    }
    if (settings['defaultProteinGoal'] != null) {
      await setDefaultProteinGoal(settings['defaultProteinGoal'].toDouble());
    }
    if (settings['defaultCarbGoal'] != null) {
      await setDefaultCarbGoal(settings['defaultCarbGoal'].toDouble());
    }
    if (settings['defaultFatGoal'] != null) {
      await setDefaultFatGoal(settings['defaultFatGoal'].toDouble());
    }
    if (settings['userWeight'] != null) {
      await setUserWeight(settings['userWeight'].toDouble());
    }
    if (settings['userHeight'] != null) {
      await setUserHeight(settings['userHeight'].toDouble());
    }
    if (settings['userAge'] != null) {
      await setUserAge(settings['userAge'].toInt());
    }
    if (settings['userGender'] != null) {
      await setUserGender(settings['userGender'].toString());
    }
    if (settings['userActivityLevel'] != null) {
      await setUserActivityLevel(settings['userActivityLevel'].toString());
    }
  }
}
