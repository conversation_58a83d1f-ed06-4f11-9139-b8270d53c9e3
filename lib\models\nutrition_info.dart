import 'package:json_annotation/json_annotation.dart';

part 'nutrition_info.g.dart';

@JsonSerializable()
class NutritionInfo {
  final double calories;
  final double protein; // in grams
  final double carbohydrates; // in grams
  final double fat; // in grams
  final double fiber; // in grams
  final double sugar; // in grams
  final double sodium; // in milligrams
  final double cholesterol; // in milligrams
  final double vitaminC; // in milligrams
  final double calcium; // in milligrams
  final double iron; // in milligrams

  const NutritionInfo({
    required this.calories,
    required this.protein,
    required this.carbohydrates,
    required this.fat,
    this.fiber = 0.0,
    this.sugar = 0.0,
    this.sodium = 0.0,
    this.cholesterol = 0.0,
    this.vitaminC = 0.0,
    this.calcium = 0.0,
    this.iron = 0.0,
  });

  factory NutritionInfo.fromJson(Map<String, dynamic> json) =>
      _$NutritionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$NutritionInfoToJson(this);

  // Calculate total macronutrients in calories
  double get totalMacroCalories => (protein * 4) + (carbohydrates * 4) + (fat * 9);

  // Get macronutrient percentages
  double get proteinPercentage => calories > 0 ? (protein * 4) / calories * 100 : 0;
  double get carbohydratePercentage => calories > 0 ? (carbohydrates * 4) / calories * 100 : 0;
  double get fatPercentage => calories > 0 ? (fat * 9) / calories * 100 : 0;

  @override
  String toString() {
    return 'NutritionInfo(calories: $calories, protein: ${protein}g, carbs: ${carbohydrates}g, fat: ${fat}g)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NutritionInfo &&
        other.calories == calories &&
        other.protein == protein &&
        other.carbohydrates == carbohydrates &&
        other.fat == fat &&
        other.fiber == fiber &&
        other.sugar == sugar &&
        other.sodium == sodium &&
        other.cholesterol == cholesterol &&
        other.vitaminC == vitaminC &&
        other.calcium == calcium &&
        other.iron == iron;
  }

  @override
  int get hashCode {
    return Object.hash(
      calories,
      protein,
      carbohydrates,
      fat,
      fiber,
      sugar,
      sodium,
      cholesterol,
      vitaminC,
      calcium,
      iron,
    );
  }

  // Create a copy with updated values
  NutritionInfo copyWith({
    double? calories,
    double? protein,
    double? carbohydrates,
    double? fat,
    double? fiber,
    double? sugar,
    double? sodium,
    double? cholesterol,
    double? vitaminC,
    double? calcium,
    double? iron,
  }) {
    return NutritionInfo(
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      fat: fat ?? this.fat,
      fiber: fiber ?? this.fiber,
      sugar: sugar ?? this.sugar,
      sodium: sodium ?? this.sodium,
      cholesterol: cholesterol ?? this.cholesterol,
      vitaminC: vitaminC ?? this.vitaminC,
      calcium: calcium ?? this.calcium,
      iron: iron ?? this.iron,
    );
  }

  // Scale nutrition info by serving size multiplier
  NutritionInfo scale(double multiplier) {
    return NutritionInfo(
      calories: calories * multiplier,
      protein: protein * multiplier,
      carbohydrates: carbohydrates * multiplier,
      fat: fat * multiplier,
      fiber: fiber * multiplier,
      sugar: sugar * multiplier,
      sodium: sodium * multiplier,
      cholesterol: cholesterol * multiplier,
      vitaminC: vitaminC * multiplier,
      calcium: calcium * multiplier,
      iron: iron * multiplier,
    );
  }

  // Add two nutrition info objects together
  NutritionInfo operator +(NutritionInfo other) {
    return NutritionInfo(
      calories: calories + other.calories,
      protein: protein + other.protein,
      carbohydrates: carbohydrates + other.carbohydrates,
      fat: fat + other.fat,
      fiber: fiber + other.fiber,
      sugar: sugar + other.sugar,
      sodium: sodium + other.sodium,
      cholesterol: cholesterol + other.cholesterol,
      vitaminC: vitaminC + other.vitaminC,
      calcium: calcium + other.calcium,
      iron: iron + other.iron,
    );
  }
}
