// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FoodItem _$FoodItemFromJson(Map<String, dynamic> json) => FoodItem(
  food: Food.fromJson(json['food'] as Map<String, dynamic>),
  servingSizeGrams: (json['servingSizeGrams'] as num).toDouble(),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$FoodItemToJson(FoodItem instance) => <String, dynamic>{
  'food': instance.food,
  'servingSizeGrams': instance.servingSizeGrams,
  'notes': instance.notes,
};

Meal _$MealFromJson(Map<String, dynamic> json) => Meal(
  id: json['id'] as String,
  name: json['name'] as String,
  type: $enumDecode(_$MealTypeEnumMap, json['type']),
  foodItems: (json['foodItems'] as List<dynamic>)
      .map((e) => FoodItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  imageUrl: json['imageUrl'] as String?,
  notes: json['notes'] as String?,
  isManuallyAdjusted: json['isManuallyAdjusted'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$MealToJson(Meal instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'type': _$MealTypeEnumMap[instance.type]!,
  'foodItems': instance.foodItems,
  'timestamp': instance.timestamp.toIso8601String(),
  'imageUrl': instance.imageUrl,
  'notes': instance.notes,
  'isManuallyAdjusted': instance.isManuallyAdjusted,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$MealTypeEnumMap = {
  MealType.breakfast: 'breakfast',
  MealType.lunch: 'lunch',
  MealType.dinner: 'dinner',
  MealType.snack: 'snack',
  MealType.other: 'other',
};
