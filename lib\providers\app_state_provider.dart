import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';

class AppStateProvider extends ChangeNotifier {
  // Services
  late final ConfigService _configService;
  late final DatabaseService _databaseService;
  late final FoodRecognitionService _foodRecognitionService;

  // State
  DailyLog? _currentDailyLog;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  DailyLog? get currentDailyLog => _currentDailyLog;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  bool get hasApiKey => _configService.hasGeminiApiKey();

  // Initialize the app state
  Future<void> initialize() async {
    if (_isInitialized) return;

    _setLoading(true);
    try {
      // Initialize services
      _configService = await ConfigService.getInstance();
      _databaseService = await DatabaseService.getInstance();
      
      if (_configService.hasGeminiApiKey()) {
        _foodRecognitionService = await FoodRecognitionService.getInstance();
      }

      // Load today's daily log
      await _loadTodaysDailyLog();

      _isInitialized = true;
      _clearError();
    } catch (e) {
      _setError('Failed to initialize app: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load today's daily log
  Future<void> _loadTodaysDailyLog() async {
    try {
      final today = DateTime.now();
      _currentDailyLog = await _databaseService.getDailyLogForDate(today);

      if (_currentDailyLog == null) {
        // Create a new daily log for today
        final goals = _getDefaultGoals();
        _currentDailyLog = DailyLog.forToday(goals: goals);
        await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);
      }
    } catch (e) {
      _setError('Failed to load daily log: $e');
    }
  }

  // Get default daily goals
  DailyGoals _getDefaultGoals() {
    if (_configService.isUserProfileComplete()) {
      final calculatedGoals = _configService.getCalculatedDailyGoals();
      return DailyGoals(
        calorieGoal: calculatedGoals['calories']!,
        proteinGoal: calculatedGoals['protein']!,
        carbohydrateGoal: calculatedGoals['carbohydrates']!,
        fatGoal: calculatedGoals['fat']!,
      );
    } else {
      return DailyGoals(
        calorieGoal: _configService.getDefaultCalorieGoal(),
        proteinGoal: _configService.getDefaultProteinGoal(),
        carbohydrateGoal: _configService.getDefaultCarbGoal(),
        fatGoal: _configService.getDefaultFatGoal(),
      );
    }
  }

  // Add meal to today's log
  Future<void> addMeal(Meal meal) async {
    if (_currentDailyLog == null) return;

    _setLoading(true);
    try {
      // Save meal to database
      await _databaseService.insertMeal(meal);

      // Update current daily log
      _currentDailyLog = _currentDailyLog!.addMeal(meal);
      await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add meal: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update meal in today's log
  Future<void> updateMeal(Meal updatedMeal) async {
    if (_currentDailyLog == null) return;

    _setLoading(true);
    try {
      // Update meal in database
      await _databaseService.insertMeal(updatedMeal);

      // Update current daily log
      _currentDailyLog = _currentDailyLog!.updateMeal(updatedMeal);
      await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update meal: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Delete meal from today's log
  Future<void> deleteMeal(String mealId) async {
    if (_currentDailyLog == null) return;

    _setLoading(true);
    try {
      // Delete meal from database
      await _databaseService.deleteMeal(mealId);

      // Update current daily log
      _currentDailyLog = _currentDailyLog!.removeMeal(mealId);
      await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete meal: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update daily goals
  Future<void> updateDailyGoals(DailyGoals newGoals) async {
    if (_currentDailyLog == null) return;

    _setLoading(true);
    try {
      _currentDailyLog = _currentDailyLog!.updateGoals(newGoals);
      await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update goals: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update weight
  Future<void> updateWeight(double weight) async {
    if (_currentDailyLog == null) return;

    _setLoading(true);
    try {
      _currentDailyLog = _currentDailyLog!.updateWeight(weight);
      await _databaseService.insertOrUpdateDailyLog(_currentDailyLog!);

      // Also update user profile
      await _configService.setUserWeight(weight);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update weight: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set API key
  Future<void> setApiKey(String apiKey) async {
    _setLoading(true);
    try {
      await _configService.setGeminiApiKey(apiKey);
      
      // Initialize food recognition service
      _foodRecognitionService = await FoodRecognitionService.getInstance();

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to set API key: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh today's data
  Future<void> refreshTodaysData() async {
    _setLoading(true);
    try {
      await _loadTodaysDailyLog();
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh data: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get nutrition summary for today
  Map<String, dynamic> get todaysNutritionSummary {
    if (_currentDailyLog == null) {
      return {
        'calories': 0.0,
        'protein': 0.0,
        'carbohydrates': 0.0,
        'fat': 0.0,
        'calorieProgress': 0.0,
        'proteinProgress': 0.0,
        'carbohydrateProgress': 0.0,
        'fatProgress': 0.0,
      };
    }

    final nutrition = _currentDailyLog!.totalNutrition;
    final goals = _currentDailyLog!.goals;

    return {
      'calories': nutrition.calories,
      'protein': nutrition.protein,
      'carbohydrates': nutrition.carbohydrates,
      'fat': nutrition.fat,
      'calorieProgress': goals.calorieGoal > 0 ? nutrition.calories / goals.calorieGoal : 0.0,
      'proteinProgress': goals.proteinGoal > 0 ? nutrition.protein / goals.proteinGoal : 0.0,
      'carbohydrateProgress': goals.carbohydrateGoal > 0 ? nutrition.carbohydrates / goals.carbohydrateGoal : 0.0,
      'fatProgress': goals.fatGoal > 0 ? nutrition.fat / goals.fatGoal : 0.0,
    };
  }

  // Get meals for today grouped by type
  Map<MealType, List<Meal>> get todaysMealsByType {
    if (_currentDailyLog == null) return {};

    final Map<MealType, List<Meal>> mealsByType = {};
    for (final mealType in MealType.values) {
      mealsByType[mealType] = _currentDailyLog!.getMealsByType(mealType);
    }
    return mealsByType;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Get food recognition service
  FoodRecognitionService? get foodRecognitionService {
    return hasApiKey ? _foodRecognitionService : null;
  }

  // Get config service
  ConfigService get configService => _configService;

  // Get database service
  DatabaseService get databaseService => _databaseService;
}
