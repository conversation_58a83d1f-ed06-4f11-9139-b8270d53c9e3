import 'package:json_annotation/json_annotation.dart';
import 'meal.dart';
import 'nutrition_info.dart';

part 'daily_log.g.dart';

@JsonSerializable()
class DailyGoals {
  final double calorieGoal;
  final double proteinGoal; // in grams
  final double carbohydrateGoal; // in grams
  final double fatGoal; // in grams
  final double fiberGoal; // in grams
  final double sodiumLimit; // in milligrams

  const DailyGoals({
    required this.calorieGoal,
    required this.proteinGoal,
    required this.carbohydrateGoal,
    required this.fatGoal,
    this.fiberGoal = 25.0,
    this.sodiumLimit = 2300.0,
  });

  factory DailyGoals.fromJson(Map<String, dynamic> json) => _$DailyGoalsFromJson(json);

  Map<String, dynamic> toJson() => _$DailyGoalsToJson(this);

  // Default goals based on a 2000 calorie diet
  factory DailyGoals.defaultGoals() {
    return const DailyGoals(
      calorieGoal: 2000,
      proteinGoal: 150, // 30% of calories
      carbohydrateGoal: 250, // 50% of calories
      fatGoal: 67, // 20% of calories
      fiberGoal: 25,
      sodiumLimit: 2300,
    );
  }

  @override
  String toString() {
    return 'DailyGoals(calories: $calorieGoal, protein: ${proteinGoal}g, carbs: ${carbohydrateGoal}g, fat: ${fatGoal}g)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyGoals &&
        other.calorieGoal == calorieGoal &&
        other.proteinGoal == proteinGoal &&
        other.carbohydrateGoal == carbohydrateGoal &&
        other.fatGoal == fatGoal &&
        other.fiberGoal == fiberGoal &&
        other.sodiumLimit == sodiumLimit;
  }

  @override
  int get hashCode {
    return Object.hash(
      calorieGoal,
      proteinGoal,
      carbohydrateGoal,
      fatGoal,
      fiberGoal,
      sodiumLimit,
    );
  }

  DailyGoals copyWith({
    double? calorieGoal,
    double? proteinGoal,
    double? carbohydrateGoal,
    double? fatGoal,
    double? fiberGoal,
    double? sodiumLimit,
  }) {
    return DailyGoals(
      calorieGoal: calorieGoal ?? this.calorieGoal,
      proteinGoal: proteinGoal ?? this.proteinGoal,
      carbohydrateGoal: carbohydrateGoal ?? this.carbohydrateGoal,
      fatGoal: fatGoal ?? this.fatGoal,
      fiberGoal: fiberGoal ?? this.fiberGoal,
      sodiumLimit: sodiumLimit ?? this.sodiumLimit,
    );
  }
}

@JsonSerializable()
class DailyLog {
  final String id;
  final DateTime date;
  final List<Meal> meals;
  final DailyGoals goals;
  final double? weight; // in kg
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DailyLog({
    required this.id,
    required this.date,
    required this.meals,
    required this.goals,
    this.weight,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DailyLog.fromJson(Map<String, dynamic> json) => _$DailyLogFromJson(json);

  Map<String, dynamic> toJson() => _$DailyLogToJson(this);

  // Calculate total nutrition for the day
  NutritionInfo get totalNutrition {
    if (meals.isEmpty) {
      return const NutritionInfo(
        calories: 0,
        protein: 0,
        carbohydrates: 0,
        fat: 0,
      );
    }

    return meals
        .map((meal) => meal.totalNutrition)
        .reduce((total, nutrition) => total + nutrition);
  }

  // Get meals by type
  List<Meal> getMealsByType(MealType type) {
    return meals.where((meal) => meal.type == type).toList();
  }

  // Calculate progress towards goals
  double get calorieProgress => goals.calorieGoal > 0 ? totalNutrition.calories / goals.calorieGoal : 0;
  double get proteinProgress => goals.proteinGoal > 0 ? totalNutrition.protein / goals.proteinGoal : 0;
  double get carbohydrateProgress => goals.carbohydrateGoal > 0 ? totalNutrition.carbohydrates / goals.carbohydrateGoal : 0;
  double get fatProgress => goals.fatGoal > 0 ? totalNutrition.fat / goals.fatGoal : 0;

  // Calculate remaining calories
  double get remainingCalories => goals.calorieGoal - totalNutrition.calories;

  // Check if daily goals are met
  bool get isCalorieGoalMet => totalNutrition.calories >= goals.calorieGoal * 0.95; // 95% tolerance
  bool get isProteinGoalMet => totalNutrition.protein >= goals.proteinGoal * 0.9; // 90% tolerance
  bool get isWithinSodiumLimit => totalNutrition.sodium <= goals.sodiumLimit;

  // Get summary statistics
  Map<String, dynamic> get summary {
    final nutrition = totalNutrition;
    return {
      'totalCalories': nutrition.calories,
      'totalMeals': meals.length,
      'calorieProgress': (calorieProgress * 100).clamp(0, 100),
      'proteinProgress': (proteinProgress * 100).clamp(0, 100),
      'carbohydrateProgress': (carbohydrateProgress * 100).clamp(0, 100),
      'fatProgress': (fatProgress * 100).clamp(0, 100),
      'remainingCalories': remainingCalories,
      'isGoalMet': isCalorieGoalMet,
    };
  }

  @override
  String toString() {
    return 'DailyLog(date: ${date.toIso8601String().split('T')[0]}, meals: ${meals.length}, calories: ${totalNutrition.calories.toStringAsFixed(0)}/${goals.calorieGoal.toStringAsFixed(0)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyLog &&
        other.id == id &&
        other.date == date &&
        other.goals == goals &&
        other.weight == weight &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(id, date, goals, weight, notes);
  }

  DailyLog copyWith({
    String? id,
    DateTime? date,
    List<Meal>? meals,
    DailyGoals? goals,
    double? weight,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyLog(
      id: id ?? this.id,
      date: date ?? this.date,
      meals: meals ?? this.meals,
      goals: goals ?? this.goals,
      weight: weight ?? this.weight,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Create a new daily log for today
  factory DailyLog.forToday({DailyGoals? goals}) {
    final today = DateTime.now();
    final dateOnly = DateTime(today.year, today.month, today.day);
    
    return DailyLog(
      id: 'daily_log_${dateOnly.millisecondsSinceEpoch}',
      date: dateOnly,
      meals: [],
      goals: goals ?? DailyGoals.defaultGoals(),
      createdAt: today,
      updatedAt: today,
    );
  }

  // Add a meal to the daily log
  DailyLog addMeal(Meal meal) {
    final updatedMeals = List<Meal>.from(meals)..add(meal);
    return copyWith(
      meals: updatedMeals,
      updatedAt: DateTime.now(),
    );
  }

  // Remove a meal from the daily log
  DailyLog removeMeal(String mealId) {
    final updatedMeals = meals.where((meal) => meal.id != mealId).toList();
    return copyWith(
      meals: updatedMeals,
      updatedAt: DateTime.now(),
    );
  }

  // Update a meal in the daily log
  DailyLog updateMeal(Meal updatedMeal) {
    final updatedMeals = meals.map((meal) {
      return meal.id == updatedMeal.id ? updatedMeal : meal;
    }).toList();
    
    return copyWith(
      meals: updatedMeals,
      updatedAt: DateTime.now(),
    );
  }

  // Update daily goals
  DailyLog updateGoals(DailyGoals newGoals) {
    return copyWith(
      goals: newGoals,
      updatedAt: DateTime.now(),
    );
  }

  // Update weight
  DailyLog updateWeight(double newWeight) {
    return copyWith(
      weight: newWeight,
      updatedAt: DateTime.now(),
    );
  }
}
