// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DailyGoals _$DailyGoalsFromJson(Map<String, dynamic> json) => DailyGoals(
  calorieGoal: (json['calorieGoal'] as num).toDouble(),
  proteinGoal: (json['proteinGoal'] as num).toDouble(),
  carbohydrateGoal: (json['carbohydrateGoal'] as num).toDouble(),
  fatGoal: (json['fatGoal'] as num).toDouble(),
  fiberGoal: (json['fiberGoal'] as num?)?.toDouble() ?? 25.0,
  sodiumLimit: (json['sodiumLimit'] as num?)?.toDouble() ?? 2300.0,
);

Map<String, dynamic> _$DailyGoalsToJson(DailyGoals instance) =>
    <String, dynamic>{
      'calorieGoal': instance.calorieGoal,
      'proteinGoal': instance.proteinGoal,
      'carbohydrateGoal': instance.carbohydrateGoal,
      'fatGoal': instance.fatGoal,
      'fiberGoal': instance.fiberGoal,
      'sodiumLimit': instance.sodiumLimit,
    };

DailyLog _$DailyLogFromJson(Map<String, dynamic> json) => DailyLog(
  id: json['id'] as String,
  date: DateTime.parse(json['date'] as String),
  meals: (json['meals'] as List<dynamic>)
      .map((e) => Meal.fromJson(e as Map<String, dynamic>))
      .toList(),
  goals: DailyGoals.fromJson(json['goals'] as Map<String, dynamic>),
  weight: (json['weight'] as num?)?.toDouble(),
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$DailyLogToJson(DailyLog instance) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date.toIso8601String(),
  'meals': instance.meals,
  'goals': instance.goals,
  'weight': instance.weight,
  'notes': instance.notes,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
