import 'dart:io';
import '../models/models.dart';
import 'services.dart';

class FoodRecognitionService {
  static FoodRecognitionService? _instance;
  
  late final GeminiApiService _geminiService;
  late final CameraService _cameraService;
  late final DatabaseService _databaseService;
  late final ConfigService _configService;

  FoodRecognitionService._();

  static Future<FoodRecognitionService> getInstance() async {
    if (_instance == null) {
      _instance = FoodRecognitionService._();
      await _instance!._initialize();
    }
    return _instance!;
  }

  Future<void> _initialize() async {
    _cameraService = await CameraService.getInstance();
    _databaseService = await DatabaseService.getInstance();
    _configService = await ConfigService.getInstance();
    
    final apiKey = _configService.getGeminiApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw FoodRecognitionException('Gemini API key not configured');
    }
    
    _geminiService = GeminiApiService(apiKey: apiKey);
  }

  /// Capture image and analyze food
  Future<FoodRecognitionResult> captureAndAnalyzeFood() async {
    try {
      // Check permissions
      if (!await _cameraService.hasCameraPermission()) {
        final granted = await _cameraService.requestCameraPermission();
        if (!granted) {
          throw FoodRecognitionException('Camera permission denied');
        }
      }

      // Take picture
      final imageFile = await _cameraService.takePictureWithImagePicker();
      if (imageFile == null) {
        throw FoodRecognitionException('No image captured');
      }

      return await analyzeImageFile(imageFile);
    } catch (e) {
      if (e is FoodRecognitionException) rethrow;
      throw FoodRecognitionException('Failed to capture and analyze food: $e');
    }
  }

  /// Pick image from gallery and analyze food
  Future<FoodRecognitionResult> pickAndAnalyzeFood() async {
    try {
      // Pick image from gallery
      final imageFile = await _cameraService.pickImageFromGallery();
      if (imageFile == null) {
        throw FoodRecognitionException('No image selected');
      }

      return await analyzeImageFile(imageFile);
    } catch (e) {
      if (e is FoodRecognitionException) rethrow;
      throw FoodRecognitionException('Failed to pick and analyze food: $e');
    }
  }

  /// Analyze an existing image file
  Future<FoodRecognitionResult> analyzeImageFile(File imageFile) async {
    try {
      // Validate image file
      if (!await imageFile.exists()) {
        throw FoodRecognitionException('Image file does not exist');
      }

      final fileSize = await _cameraService.getImageFileSize(imageFile);
      if (fileSize == 0) {
        throw FoodRecognitionException('Image file is empty');
      }

      // Save image to app directory
      final savedImageFile = await _cameraService.saveImageToAppDirectory(imageFile);

      // Analyze with Gemini API
      final recognizedFoods = await _geminiService.analyzeFoodImage(savedImageFile);

      if (recognizedFoods.isEmpty) {
        return FoodRecognitionResult(
          imageFile: savedImageFile,
          recognizedFoods: [],
          success: false,
          message: 'No food items were recognized in the image',
        );
      }

      // Save recognized foods to database
      for (final food in recognizedFoods) {
        await _databaseService.insertFood(food);
      }

      // Create food items with estimated serving sizes
      final foodItems = recognizedFoods.map((food) {
        // Use a default serving size of 100g, but this could be improved
        // with better portion estimation from the image
        final servingSize = _estimateServingSize(food);
        return FoodItem(
          food: food,
          servingSizeGrams: servingSize,
          notes: food.confidence < 0.8 ? 'Low confidence recognition' : null,
        );
      }).toList();

      return FoodRecognitionResult(
        imageFile: savedImageFile,
        recognizedFoods: recognizedFoods,
        foodItems: foodItems,
        success: true,
        message: 'Successfully recognized ${recognizedFoods.length} food item(s)',
      );
    } catch (e) {
      if (e is FoodRecognitionException) rethrow;
      if (e is GeminiApiException) {
        throw FoodRecognitionException('AI analysis failed: ${e.message}');
      }
      throw FoodRecognitionException('Failed to analyze image: $e');
    }
  }

  /// Create a meal from recognition result
  Future<Meal> createMealFromRecognition(
    FoodRecognitionResult result, {
    MealType? mealType,
    String? notes,
  }) async {
    if (!result.success || result.foodItems.isEmpty) {
      throw FoodRecognitionException('Cannot create meal from unsuccessful recognition');
    }

    final meal = Meal.fromCameraCapture(
      imageUrl: result.imageFile.path,
      recognizedFoods: result.foodItems,
      type: mealType,
      notes: notes,
    );

    // Save meal to database
    await _databaseService.insertMeal(meal);

    return meal;
  }

  /// Estimate serving size based on food type and image analysis
  double _estimateServingSize(Food food) {
    // This is a simplified estimation. In a real app, you might want to:
    // 1. Use computer vision to estimate portion sizes
    // 2. Ask the user to confirm/adjust serving sizes
    // 3. Use machine learning models trained on food portions
    
    final category = food.category.toLowerCase();
    
    // Default serving sizes by category (in grams)
    switch (category) {
      case 'fruits':
        return 150.0; // Medium fruit
      case 'vegetables':
        return 100.0; // Standard serving
      case 'grains':
      case 'bread':
        return 80.0; // Slice of bread or small portion
      case 'protein':
      case 'meat':
      case 'fish':
        return 120.0; // Standard protein serving
      case 'dairy':
        return 200.0; // Glass of milk or yogurt
      case 'snacks':
        return 30.0; // Small snack portion
      case 'beverages':
        return 250.0; // Standard drink
      default:
        return 100.0; // Default 100g
    }
  }

  /// Get recognition history
  Future<List<Food>> getRecognitionHistory({int limit = 50}) async {
    final foods = await _databaseService.getAllFoods();
    return foods.take(limit).toList();
  }

  /// Search for previously recognized foods
  Future<List<Food>> searchRecognizedFoods(String query) async {
    return await _databaseService.searchFoods(query);
  }

  /// Update API key
  Future<void> updateApiKey(String apiKey) async {
    await _configService.setGeminiApiKey(apiKey);
    _geminiService = GeminiApiService(apiKey: apiKey);
  }

  /// Check if service is properly configured
  bool isConfigured() {
    return _configService.hasGeminiApiKey();
  }

  /// Get service status
  FoodRecognitionServiceStatus getStatus() {
    final hasApiKey = _configService.hasGeminiApiKey();
    final hasCamera = _cameraService.hasCamera();
    
    return FoodRecognitionServiceStatus(
      isConfigured: hasApiKey,
      hasCameraAccess: hasCamera,
      apiKeyConfigured: hasApiKey,
    );
  }

  /// Cleanup resources
  void dispose() {
    _geminiService.dispose();
  }
}

/// Result of food recognition analysis
class FoodRecognitionResult {
  final File imageFile;
  final List<Food> recognizedFoods;
  final List<FoodItem> foodItems;
  final bool success;
  final String message;
  final double? totalConfidence;

  FoodRecognitionResult({
    required this.imageFile,
    required this.recognizedFoods,
    this.foodItems = const [],
    required this.success,
    required this.message,
    this.totalConfidence,
  });

  /// Get average confidence of all recognized foods
  double get averageConfidence {
    if (recognizedFoods.isEmpty) return 0.0;
    final total = recognizedFoods.fold(0.0, (sum, food) => sum + food.confidence);
    return total / recognizedFoods.length;
  }

  /// Check if any foods have low confidence
  bool get hasLowConfidenceItems {
    return recognizedFoods.any((food) => food.needsVerification);
  }

  /// Get total estimated calories
  double get totalCalories {
    return foodItems.fold(0.0, (sum, item) => sum + item.nutrition.calories);
  }

  /// Get summary of recognized foods
  String get summary {
    if (recognizedFoods.isEmpty) return 'No foods recognized';
    
    final foodNames = recognizedFoods.map((food) => food.name).join(', ');
    return 'Recognized: $foodNames';
  }
}

/// Status of the food recognition service
class FoodRecognitionServiceStatus {
  final bool isConfigured;
  final bool hasCameraAccess;
  final bool apiKeyConfigured;

  const FoodRecognitionServiceStatus({
    required this.isConfigured,
    required this.hasCameraAccess,
    required this.apiKeyConfigured,
  });

  bool get isReady => isConfigured && hasCameraAccess && apiKeyConfigured;

  String get statusMessage {
    if (!apiKeyConfigured) return 'API key not configured';
    if (!hasCameraAccess) return 'Camera access not available';
    if (!isConfigured) return 'Service not properly configured';
    return 'Ready';
  }
}

/// Exception for food recognition errors
class FoodRecognitionException implements Exception {
  final String message;
  
  const FoodRecognitionException(this.message);
  
  @override
  String toString() => 'FoodRecognitionException: $message';
}
