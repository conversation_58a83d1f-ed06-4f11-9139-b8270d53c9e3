import 'package:json_annotation/json_annotation.dart';
import 'food.dart';
import 'nutrition_info.dart';

part 'meal.g.dart';

enum MealType {
  breakfast,
  lunch,
  dinner,
  snack,
  other,
}

@JsonSerializable()
class FoodItem {
  final Food food;
  final double servingSizeGrams;
  final String? notes;

  const FoodItem({
    required this.food,
    required this.servingSizeGrams,
    this.notes,
  });

  factory FoodItem.fromJson(Map<String, dynamic> json) => _$FoodItemFromJson(json);

  Map<String, dynamic> toJson() => _$FoodItemToJson(this);

  // Get nutrition info for this specific serving
  NutritionInfo get nutrition => food.getNutritionForServing(servingSizeGrams);

  @override
  String toString() {
    return 'FoodItem(food: ${food.name}, serving: ${servingSizeGrams}g)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FoodItem &&
        other.food == food &&
        other.servingSizeGrams == servingSizeGrams &&
        other.notes == notes;
  }

  @override
  int get hashCode => Object.hash(food, servingSizeGrams, notes);

  FoodItem copyWith({
    Food? food,
    double? servingSizeGrams,
    String? notes,
  }) {
    return FoodItem(
      food: food ?? this.food,
      servingSizeGrams: servingSizeGrams ?? this.servingSizeGrams,
      notes: notes ?? this.notes,
    );
  }
}

@JsonSerializable()
class Meal {
  final String id;
  final String name;
  final MealType type;
  final List<FoodItem> foodItems;
  final DateTime timestamp;
  final String? imageUrl;
  final String? notes;
  final bool isManuallyAdjusted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Meal({
    required this.id,
    required this.name,
    required this.type,
    required this.foodItems,
    required this.timestamp,
    this.imageUrl,
    this.notes,
    this.isManuallyAdjusted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Meal.fromJson(Map<String, dynamic> json) => _$MealFromJson(json);

  Map<String, dynamic> toJson() => _$MealToJson(this);

  // Calculate total nutrition for the entire meal
  NutritionInfo get totalNutrition {
    if (foodItems.isEmpty) {
      return const NutritionInfo(
        calories: 0,
        protein: 0,
        carbohydrates: 0,
        fat: 0,
      );
    }

    return foodItems
        .map((item) => item.nutrition)
        .reduce((total, nutrition) => total + nutrition);
  }

  // Get the number of food items in this meal
  int get itemCount => foodItems.length;

  // Check if meal has any low-confidence food items
  bool get hasLowConfidenceItems => 
      foodItems.any((item) => item.food.needsVerification);

  // Get average confidence of all food items
  double get averageConfidence {
    if (foodItems.isEmpty) return 0.0;
    final totalConfidence = foodItems
        .map((item) => item.food.confidence)
        .reduce((sum, confidence) => sum + confidence);
    return totalConfidence / foodItems.length;
  }

  @override
  String toString() {
    return 'Meal(id: $id, name: $name, type: $type, items: ${foodItems.length}, calories: ${totalNutrition.calories.toStringAsFixed(0)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Meal &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.timestamp == timestamp &&
        other.imageUrl == imageUrl &&
        other.notes == notes &&
        other.isManuallyAdjusted == isManuallyAdjusted;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      type,
      timestamp,
      imageUrl,
      notes,
      isManuallyAdjusted,
    );
  }

  Meal copyWith({
    String? id,
    String? name,
    MealType? type,
    List<FoodItem>? foodItems,
    DateTime? timestamp,
    String? imageUrl,
    String? notes,
    bool? isManuallyAdjusted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Meal(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      foodItems: foodItems ?? this.foodItems,
      timestamp: timestamp ?? this.timestamp,
      imageUrl: imageUrl ?? this.imageUrl,
      notes: notes ?? this.notes,
      isManuallyAdjusted: isManuallyAdjusted ?? this.isManuallyAdjusted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Create a meal from camera capture
  factory Meal.fromCameraCapture({
    required String imageUrl,
    required List<FoodItem> recognizedFoods,
    MealType? type,
    String? notes,
  }) {
    final now = DateTime.now();
    final mealType = type ?? _determineMealTypeFromTime(now);
    
    return Meal(
      id: 'meal_${now.millisecondsSinceEpoch}',
      name: _generateMealName(mealType, now),
      type: mealType,
      foodItems: recognizedFoods,
      timestamp: now,
      imageUrl: imageUrl,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Helper method to determine meal type based on time
  static MealType _determineMealTypeFromTime(DateTime dateTime) {
    final hour = dateTime.hour;
    if (hour >= 5 && hour < 11) return MealType.breakfast;
    if (hour >= 11 && hour < 16) return MealType.lunch;
    if (hour >= 16 && hour < 22) return MealType.dinner;
    return MealType.snack;
  }

  // Helper method to generate meal name
  static String _generateMealName(MealType type, DateTime dateTime) {
    final typeNames = {
      MealType.breakfast: 'Breakfast',
      MealType.lunch: 'Lunch',
      MealType.dinner: 'Dinner',
      MealType.snack: 'Snack',
      MealType.other: 'Meal',
    };
    
    final timeString = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    return '${typeNames[type]} at $timeString';
  }

  // Add a food item to the meal
  Meal addFoodItem(FoodItem foodItem) {
    final updatedFoodItems = List<FoodItem>.from(foodItems)..add(foodItem);
    return copyWith(
      foodItems: updatedFoodItems,
      updatedAt: DateTime.now(),
    );
  }

  // Remove a food item from the meal
  Meal removeFoodItem(int index) {
    if (index < 0 || index >= foodItems.length) return this;
    final updatedFoodItems = List<FoodItem>.from(foodItems)..removeAt(index);
    return copyWith(
      foodItems: updatedFoodItems,
      updatedAt: DateTime.now(),
    );
  }

  // Update a food item in the meal
  Meal updateFoodItem(int index, FoodItem foodItem) {
    if (index < 0 || index >= foodItems.length) return this;
    final updatedFoodItems = List<FoodItem>.from(foodItems);
    updatedFoodItems[index] = foodItem;
    return copyWith(
      foodItems: updatedFoodItems,
      isManuallyAdjusted: true,
      updatedAt: DateTime.now(),
    );
  }
}
