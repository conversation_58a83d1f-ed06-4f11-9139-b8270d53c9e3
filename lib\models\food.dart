import 'package:json_annotation/json_annotation.dart';
import 'nutrition_info.dart';

part 'food.g.dart';

@JsonSerializable()
class Food {
  final String id;
  final String name;
  final String description;
  final NutritionInfo nutritionPer100g;
  final String category;
  final List<String> tags;
  final double confidence; // AI recognition confidence (0.0 to 1.0)
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Food({
    required this.id,
    required this.name,
    required this.description,
    required this.nutritionPer100g,
    required this.category,
    this.tags = const [],
    this.confidence = 1.0,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Food.fromJson(Map<String, dynamic> json) => _$FoodFromJson(json);

  Map<String, dynamic> toJson() => _$FoodToJson(this);

  // Calculate nutrition for a specific serving size in grams
  NutritionInfo getNutritionForServing(double servingSizeGrams) {
    final multiplier = servingSizeGrams / 100.0;
    return nutritionPer100g.scale(multiplier);
  }

  // Common serving sizes
  static const Map<String, double> commonServingSizes = {
    'Small portion': 50.0,
    'Medium portion': 100.0,
    'Large portion': 150.0,
    'Extra large portion': 200.0,
  };

  @override
  String toString() {
    return 'Food(id: $id, name: $name, category: $category, confidence: ${(confidence * 100).toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Food &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.nutritionPer100g == nutritionPer100g &&
        other.category == category &&
        other.confidence == confidence &&
        other.imageUrl == imageUrl;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      nutritionPer100g,
      category,
      confidence,
      imageUrl,
    );
  }

  Food copyWith({
    String? id,
    String? name,
    String? description,
    NutritionInfo? nutritionPer100g,
    String? category,
    List<String>? tags,
    double? confidence,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Food(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      nutritionPer100g: nutritionPer100g ?? this.nutritionPer100g,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      confidence: confidence ?? this.confidence,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Create a food item from Gemini API response
  factory Food.fromGeminiResponse({
    required String name,
    required String description,
    required String category,
    required NutritionInfo nutrition,
    required double confidence,
    List<String> tags = const [],
  }) {
    final now = DateTime.now();
    return Food(
      id: '${name.toLowerCase().replaceAll(' ', '_')}_${now.millisecondsSinceEpoch}',
      name: name,
      description: description,
      nutritionPer100g: nutrition,
      category: category,
      tags: tags,
      confidence: confidence,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Check if this food item has high confidence recognition
  bool get isHighConfidence => confidence >= 0.8;

  // Check if this food item needs manual verification
  bool get needsVerification => confidence < 0.6;

  // Get a user-friendly confidence description
  String get confidenceDescription {
    if (confidence >= 0.9) return 'Very High';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    if (confidence >= 0.4) return 'Low';
    return 'Very Low';
  }
}
